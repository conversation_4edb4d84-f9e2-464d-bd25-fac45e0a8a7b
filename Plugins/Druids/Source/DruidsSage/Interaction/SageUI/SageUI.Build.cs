using UnrealBuildTool;

public class SageUI : ModuleRules
{
    public SageUI(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",

                "UMG",

                "Json",
                "InputCore",
            }
        );

        // Editor-only dependencies
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "AssetRegistry",
                }
            );
        }
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",

                "SageCommonTypes",
                "SageCore",
                "SageNetworking",
            });
    }
}
