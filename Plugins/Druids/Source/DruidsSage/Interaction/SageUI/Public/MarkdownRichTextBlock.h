#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Engine/Engine.h"
#include "Framework/Text/TextDecorators.h"

#include "MarkdownRichTextBlock.generated.h"

// Forward declarations
class FCustomMarkdownDecorator;

UCLASS(BlueprintType, Blueprintable, meta = (DisplayName = "Markdown Rich Text Block"))
class SAGEUI_API UMarkdownRichTextBlock : public URichTextBlock
{
	GENERATED_BODY()

public:
	UMarkdownRichTextBlock(const FObjectInitializer& ObjectInitializer);

	// URichTextBlock interface
	virtual void SynchronizeProperties() override;
	virtual TSharedRef<SWidget> RebuildWidget() override;
	// End of URichTextBlock interface

protected:
	// Override to add our custom decorator to the existing decorator list
	virtual void CreateDecorators(TArray<TSharedRef<ITextDecorator>>& OutDecorators) override;



public:
	// Configuration properties (using different names to avoid shadowing base class properties)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	bool bMarkdownAutoWrapText = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownWrapTextAt = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FMargin MarkdownTextMargin = FMargin(0.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownLineHeightPercentage = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FText MarkdownHighlightText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	TEnumAsByte<ETextJustify::Type> MarkdownTextJustification = ETextJustify::Left;

	// Methods
	UFUNCTION(BlueprintCallable, Category = "Markdown")
	void SetNewText(const FText& InNewText);

private:
	static FText ProcessMarkdownAttribute(const FText& RawMarkdown);
	static FString ProcessInlineFormatting(const FString& InputText, const FString& BlockTag, bool bUseBlockTag = true,
	                                       bool bParentBold = false,
	                                       bool bParentItalic = false);

	// Setup custom decorators for markdown rendering
	void SetupMarkdownDecorators();

	// Store the custom decorator
	TSharedPtr<FCustomMarkdownDecorator> MarkdownDecorator;
};


