#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Blueprint/UserWidget.h"
#include "Engine/Engine.h"

#include "MarkdownRichTextBlock.generated.h"

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UMarkdownRichTextBlock : public URichTextBlock
{
	GENERATED_BODY()

public:
	UMarkdownRichTextBlock(const FObjectInitializer& ObjectInitializer);

	// URichTextBlock interface
	virtual void SynchronizeProperties() override;
	// End of URichTextBlock interface

	// Configuration properties (using different names to avoid shadowing base class properties)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	bool bMarkdownAutoWrapText = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownWrapTextAt = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FMargin MarkdownTextMargin = FMargin(0.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float MarkdownLineHeightPercentage = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FText MarkdownHighlightText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	TEnumAsByte<ETextJustify::Type> MarkdownTextJustification = ETextJustify::Left;

	// Methods
	UFUNCTION(BlueprintCallable, Category = "Markdown")
	void SetNewText(const FText& InNewText);

private:
	static FText ProcessMarkdownAttribute(const FText& RawMarkdown);
	static FString ProcessInlineFormatting(const FString& InputText, const FString& BlockTag, bool bUseBlockTag = true,
	                                       bool bParentBold = false,
	                                       bool bParentItalic = false);
};

/**
 * Wrapper widget class that can be used as a base for Widget Blueprints
 * Contains a UMarkdownRichTextBlock as its main component
 */
UCLASS(BlueprintType, Blueprintable, meta = (DisplayName = "Markdown Rich Text Widget"))
class SAGEUI_API UMarkdownRichTextWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	UMarkdownRichTextWidget(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// Public API that forwards to the internal MarkdownRichTextBlock
	UFUNCTION(BlueprintCallable, Category = "Markdown")
	void SetMarkdownText(const FText& InNewText);

	UFUNCTION(BlueprintCallable, Category = "Markdown")
	FText GetMarkdownText() const;

protected:
	// The main markdown rich text block component
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Markdown")
	UMarkdownRichTextBlock* MarkdownTextBlock;

	// Configuration properties that forward to the internal component
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	bool bAutoWrapText = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float WrapTextAt = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FMargin TextMargin = FMargin(0.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	float LineHeightPercentage = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	TEnumAsByte<ETextJustify::Type> TextJustification = ETextJustify::Left;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Markdown")
	FText MarkdownText;

private:
	void UpdateMarkdownTextBlock();
};
