#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"

// Asset loading includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

#if WITH_EDITOR
#include "Editor.h"
#endif

#define LOCTEXT_NAMESPACE "FSageUIModule"

void FSageUIModule::StartupModule()
{
    
}

void FSageUIModule::ShutdownModule()
{
	// Clear cached ChatWidgetOverrides instance
	CachedChatWidgetOverrides = nullptr;
}

UChatWidgetOverrides* FSageUIModule::GetChatWidgetOverrides()
{
	// Load if not already cached
	if (!CachedChatWidgetOverrides)
	{
		CachedChatWidgetOverrides = LoadChatWidgetOverrides();
	}
	return CachedChatWidgetOverrides;
}

UChatWidgetOverrides* FSageUIModule::LoadChatWidgetOverrides()
{
	// Return cached instance if already loaded
	if (CachedChatWidgetOverrides)
	{
		return CachedChatWidgetOverrides;
	}

	// Hard-coded path to the ChatWidgetOverrides Blueprint in the plugin's Content folder
	const FString BlueprintPath = TEXT("/Druids/ChatWidgets/ChatWidgetOverrides.ChatWidgetOverrides_C");

	// Try to load the Blueprint class
	UClass* BlueprintClass = LoadClass<UChatWidgetOverrides>(nullptr, *BlueprintPath);
	if (!BlueprintClass)
	{
		// Blueprint not found or failed to load
		UE_LOG(LogTemp, Warning, TEXT("ChatWidgetOverrides Blueprint not found at path: %s"), *BlueprintPath);
		return nullptr;
	}

	// Create an instance of the Blueprint class using the transient package as outer
#if WITH_EDITOR
	if (GEditor)
	{
		UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
		CachedChatWidgetOverrides = NewObject<UChatWidgetOverrides>(EditorWorld, BlueprintClass);
	}
#endif

	if (!CachedChatWidgetOverrides)
	{
		CachedChatWidgetOverrides = NewObject<UChatWidgetOverrides>(GetTransientPackage(), BlueprintClass);
	}

	if (!CachedChatWidgetOverrides)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create instance of ChatWidgetOverrides Blueprint"));
		return nullptr;
	}

	UE_LOG(LogTemp, Log, TEXT("Successfully loaded ChatWidgetOverrides from: %s"), *BlueprintPath);
	return CachedChatWidgetOverrides;
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageUIModule, SageUI)
